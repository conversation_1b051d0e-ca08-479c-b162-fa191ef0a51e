import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';
import { getUserFromSession } from '@/lib/auth-utils';

// GET /api/notifications/[id] - Get a specific notification
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Get user from session for filtering
    const user = await getUserFromSession(request);

    const notification = await NotificationModel.findById(id, user?.id);

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Transform the notification to include documentUrl if it has a documentId
    const transformedNotification = {
      ...notification,
      documentUrl: notification.documentId ? `/api/documents/${notification.documentId}` : undefined
    };

    return NextResponse.json({
      success: true,
      notification: transformedNotification
    });
  } catch (error) {
    console.error('Error fetching notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notification' },
      { status: 500 }
    );
  }
}

// PATCH /api/notifications/[id] - Update a notification (e.g., mark as read or update content)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { isRead, title, message, pdfFileName, documentId } = body;

    // Handle marking as read
    if (isRead === true) {
      const success = await NotificationModel.markAsRead(id);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Notification not found' },
          { status: 404 }
        );
      }

      const updatedNotification = await NotificationModel.findById(id);

      return NextResponse.json({
        success: true,
        notification: updatedNotification
      });
    }

    // Handle full notification update (for edit functionality)
    if (title || message || pdfFileName || documentId) {
      const updateData: any = {};

      if (title) updateData.title = title;
      if (message) updateData.message = message;
      if (pdfFileName) updateData.pdfFileName = pdfFileName;
      if (documentId) updateData.documentId = documentId;

      const success = await NotificationModel.update(id, updateData);

      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Notification not found' },
          { status: 404 }
        );
      }

      const updatedNotification = await NotificationModel.findById(id);

      if (!updatedNotification) {
        return NextResponse.json(
          { success: false, error: "Notification not found after update" },
          { status: 404 }
        );
      }

      // Transform the notification to include documentUrl if it has a documentId
      const transformedNotification = {
        ...updatedNotification,
        documentUrl: updatedNotification.documentId ? `/api/documents/${updatedNotification.documentId}` : undefined
      };

      return NextResponse.json({
        success: true,
        notification: transformedNotification
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid update operation' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/[id] - Delete a specific notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const success = await NotificationModel.delete(id);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}
