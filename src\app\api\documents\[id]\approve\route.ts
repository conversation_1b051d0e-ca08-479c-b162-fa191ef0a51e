import { NextRequest, NextResponse } from 'next/server';
import { DocumentModel } from '@/lib/models/document';
import { archiveDocument, createDocumentQRCode } from '@/lib/database';
import { getCurrentAdmin } from '@/lib/admin-utils';
import { getClientBaseURL } from '@/lib/network-utils';
import QRCode from 'qrcode';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { approvedBy: requestApprovedBy, updatedPdfData, approvedHtmlContent } = body;

    // Get the current admin from session
    const currentAdmin = await getCurrentAdmin(request);
    const approvedBy = currentAdmin || requestApprovedBy || 'admin';

    // Get document data from database
    const document = await DocumentModel.findById(id);

    if (!document) {
      return NextResponse.json(
        { success: false, error: 'Document not found' },
        { status: 404 }
      );
    }

    // Parse existing pdfData
    let existingPdfData;
    try {
      existingPdfData = typeof document.pdfData === 'string' 
        ? JSON.parse(document.pdfData) 
        : document.pdfData;
    } catch (parseError) {
      console.error('Error parsing existing pdfData:', parseError);
      return NextResponse.json(
        { success: false, error: 'Invalid document data format' },
        { status: 500 }
      );
    }

    if (!existingPdfData) {
      return NextResponse.json(
        { success: false, error: 'No document data available for approval' },
        { status: 400 }
      );
    }

    // Update document status to approved
    const now = new Date().toISOString();
    const updateSuccess = await DocumentModel.update(id, {
      status: 'approved',
      approvedAt: now,
      approvedBy: approvedBy,
      pdfData: updatedPdfData || existingPdfData
    });

    if (!updateSuccess) {
      return NextResponse.json(
        { success: false, error: 'Failed to update document status' },
        { status: 500 }
      );
    }

    // Prepare data for archiving
    const finalPdfData = updatedPdfData || existingPdfData;
    
    // Extract user data for archiving
    const userData = finalPdfData.userData || {};
    const firstName = userData.firstName || userData.first_name || '';
    const lastName = userData.lastName || userData.last_name || '';
    const middleInitial = userData.middleInitial || userData.middle_initial || '';
    const suffix = userData.suffix || '';
    
    const applicantName = [firstName, middleInitial, lastName, suffix]
      .filter(Boolean)
      .join(' ')
      .trim() || 'Unknown Applicant';

    // Create notification-like data structure for archiving compatibility
    const notificationData = {
      id: document.id,
      title: document.templateName,
      templateName: document.templateName,
      message: document.message,
      type: document.type,
      isRead: document.isRead,
      createdAt: new Date(document.createdAt),
      pdfFileName: document.pdfFileName,
      pdfData: {
        ...finalPdfData,
        status: 'approved',
        approvedAt: now,
        approvedBy: approvedBy,
        approvedHtmlContent: approvedHtmlContent
      },
      userId: document.userId,
      // Add extracted user data for archiving
      firstName: firstName,
      lastName: lastName,
      middleInitial: middleInitial,
      suffix: suffix,
      applicantName: applicantName
    };

    // Archive the document to SQLite database
    const archivedDoc = await archiveDocument(notificationData, approvedBy);

    // Generate QR code for the approved document
    try {
      const baseURL = await getClientBaseURL();
      const validationUrl = `${baseURL}/validate/${archivedDoc.id}`;

      // Generate QR code data URL
      const qrCodeDataUrl = await QRCode.toDataURL(validationUrl, {
        width: 200,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      // Save QR code to database
      await createDocumentQRCode({
        document_id: archivedDoc.id,
        qr_code_data: qrCodeDataUrl,
        validation_url: validationUrl,
      });

      console.log(`QR code generated for approved document ${archivedDoc.id}`);
    } catch (qrError) {
      console.error('Error generating QR code for approved document:', qrError);
      // Don't fail the approval process if QR code generation fails
    }

    // After successful archiving, delete the document from documents table
    const deleteSuccess = await DocumentModel.delete(id);

    if (!deleteSuccess) {
      console.warn(`Failed to delete document ${id} after archiving, but archiving was successful`);
    }

    return NextResponse.json({
      success: true,
      message: 'Document approved, archived, and QR code generated',
      data: archivedDoc,
      documentDeleted: deleteSuccess
    });

  } catch (error) {
    console.error('Approve document API error:', error);
    return NextResponse.json(
      { success: false, error: `Failed to approve and archive document: ${error.message}` },
      { status: 500 }
    );
  }
}
