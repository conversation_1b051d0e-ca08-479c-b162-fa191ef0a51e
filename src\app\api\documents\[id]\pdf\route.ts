import { NextRequest, NextResponse } from 'next/server';
import { DocumentModel } from '@/lib/models/document';
import fs from 'fs';
import path from 'path';

// GET /api/documents/[id]/pdf - Generate PDF from JSON metadata
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const document = await DocumentModel.findById(id);

    if (!document) {
      return NextResponse.json(
        { success: false, error: 'Document not found' },
        { status: 404 }
      );
    }

    if (!document.pdfData) {
      return NextResponse.json(
        { success: false, error: 'No document data associated with this document' },
        { status: 404 }
      );
    }

    // Parse pdfData if it's a string
    let parsedPdfData;
    try {
      parsedPdfData = typeof document.pdfData === 'string' 
        ? JSON.parse(document.pdfData) 
        : document.pdfData;
    } catch (parseError) {
      console.error('Error parsing pdfData:', parseError);
      return NextResponse.json(
        { success: false, error: 'Invalid document data format' },
        { status: 500 }
      );
    }

    // Check if we have a template file
    const templatePath = path.join(process.cwd(), 'uploads', 'templates', `${parsedPdfData.templateId}.html`);
    
    if (!fs.existsSync(templatePath)) {
      return NextResponse.json(
        { success: false, error: 'Template file not found' },
        { status: 404 }
      );
    }

    // Read the template
    let htmlContent = fs.readFileSync(templatePath, 'utf8');

    // Replace placeholders with user data
    Object.entries(parsedPdfData.userData).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      htmlContent = htmlContent.replace(new RegExp(placeholder, 'g'), value || '');
    });

    // Handle photo replacement if available
    if (parsedPdfData.photoBase64) {
      // Replace photo placeholders with base64 image
      const photoSelectors = [
        'src="{{applicant-photo}}"',
        'src="applicant-photo"',
        'src="placeholder-photo"',
        'src="default-photo"'
      ];
      
      photoSelectors.forEach(selector => {
        htmlContent = htmlContent.replace(
          new RegExp(selector, 'g'), 
          `src="${parsedPdfData.photoBase64}"`
        );
      });
    }

    // Return HTML content that can be converted to PDF on the client side
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="${parsedPdfData.templateName || 'document'}.html"`
      }
    });

  } catch (error) {
    console.error('Error generating document PDF:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate document PDF' },
      { status: 500 }
    );
  }
}
