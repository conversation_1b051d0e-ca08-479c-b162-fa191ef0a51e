import { generateUUID } from './utils';

// Server-side only imports
let Database: any;
let path: any;
let fs: any;

// Only import on server side
if (typeof window === 'undefined') {
  Database = require('better-sqlite3');
  path = require('path');
  fs = require('fs').promises;
}

// Database file path
const DB_PATH = typeof window === 'undefined' ? path?.join(process.cwd(), 'data', 'ldis.db') : '';

let db: any = null;

// Ensure data directory exists
const ensureDataDir = async (): Promise<void> => {
  const dataDir = path.dirname(DB_PATH);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
};

// Initialize database connection
export const initDatabase = async (): Promise<any> => {
  // Only run on server side
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (db) {
    return db;
  }

  await ensureDataDir();

  db = new Database(DB_PATH);

  // Enable WAL mode for better performance
  db.pragma('journal_mode = WAL');

  // Additional performance optimizations
  db.pragma('synchronous = NORMAL');
  db.pragma('cache_size = 10000');
  db.pragma('temp_store = MEMORY');
  db.pragma('mmap_size = 268435456'); // 256MB

  // Create tables if they don't exist
  await createTables();

  return db;
};

// Get database instance
export const getDatabase = async (): Promise<any> => {
  if (typeof window !== 'undefined') {
    throw new Error('Database operations can only be performed on the server side');
  }

  if (!db) {
    return await initDatabase();
  }
  return db;
};

// Create database tables
const createTables = async (): Promise<void> => {
  if (!db) return;

  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      recovery_options TEXT, -- JSON string
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Templates table
  db.exec(`
    CREATE TABLE IF NOT EXISTS templates (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      filename TEXT NOT NULL,
      placeholders TEXT, -- JSON array
      layout_size TEXT DEFAULT 'A4',
      has_applicant_photo BOOLEAN DEFAULT FALSE,
      uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Documents table - main holder for document data
  // Check if migration is needed for title -> template_name
  const documentsTableInfo = db.prepare("PRAGMA table_info(documents)").all() as any[];
  const hasTitleColumn = documentsTableInfo.some((col: any) => col.name === 'title');
  const hasTemplateNameColumn = documentsTableInfo.some((col: any) => col.name === 'template_name');

  if (documentsTableInfo.length === 0) {
    // Table doesn't exist, create with new schema
    db.exec(`
      CREATE TABLE documents (
        id TEXT PRIMARY KEY,
        template_name TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        pdf_data TEXT, -- JSON string of embedded PDF data
        user_id INTEGER REFERENCES users(id), -- Track which user/admin performed actions
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
        approved_at DATETIME,
        approved_by TEXT
      )
    `);
    console.log('✅ Created documents table with template_name column');
  } else if (hasTitleColumn && !hasTemplateNameColumn) {
    // Table exists with old schema, migrate it
    console.log('🔄 Migrating documents table from title to template_name...');

    // Create new table
    db.exec(`
      CREATE TABLE documents_new (
        id TEXT PRIMARY KEY,
        template_name TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        pdf_data TEXT, -- JSON string of embedded PDF data
        user_id INTEGER REFERENCES users(id), -- Track which user/admin performed actions
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
        approved_at DATETIME,
        approved_by TEXT
      )
    `);

    // Copy data
    const copyResult = db.prepare(`
      INSERT INTO documents_new (
        id, template_name, message, type, is_read, created_at, updated_at,
        pdf_filename, pdf_data, user_id, status, approved_at, approved_by
      )
      SELECT
        id, title as template_name, message, type, is_read, created_at, updated_at,
        pdf_filename, pdf_data, user_id, status, approved_at, approved_by
      FROM documents
    `).run();

    // Replace old table
    db.exec('DROP TABLE documents');
    db.exec('ALTER TABLE documents_new RENAME TO documents');

    console.log(`✅ Migrated ${copyResult.changes} document records to new schema`);
  }

  // Check if notifications table needs migration (remove old columns)
  const tableInfo = db.prepare("PRAGMA table_info(notifications)").all() as any[];
  const hasOldColumns = tableInfo.some((col: any) => col.name === 'user_id' || col.name === 'pdf_file_path');

  if (hasOldColumns) {
    console.log('Migrating notifications table to remove unused columns...');

    // Backup existing data
    const existingData = db.prepare('SELECT id, title, message, type, is_read, created_at, updated_at, pdf_filename, user_id FROM notifications').all();

    // Drop old table
    db.exec('DROP TABLE IF EXISTS notifications');

    // Create new table with clean schema (without pdf_data, with document_id)
    db.exec(`
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        user_id INTEGER REFERENCES users(id), -- Track which user/admin performed actions
        document_id TEXT REFERENCES documents(id) -- Reference to document if applicable
      )
    `);

    // Restore data (without pdf_data, with document_id)
    if (existingData.length > 0) {
      const insertStmt = db.prepare(`
        INSERT INTO notifications (id, title, message, type, is_read, created_at, updated_at, pdf_filename, user_id, document_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const row of existingData) {
        insertStmt.run(
          row.id,
          row.title,
          row.message,
          row.type,
          row.is_read,
          row.created_at,
          row.updated_at,
          row.pdf_filename,
          null, // user_id will be null for existing records
          null  // document_id will be null for existing records
        );
      }
      console.log(`Migrated ${existingData.length} notification records`);
    }
  } else {
    // Create table with new schema if it doesn't exist (without pdf_data, with document_id)
    db.exec(`
      CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
        is_read BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        pdf_filename TEXT,
        user_id INTEGER REFERENCES users(id), -- Track which user/admin performed actions
        document_id TEXT REFERENCES documents(id) -- Reference to document if applicable
      )
    `);
  }

  // Archives table for approved documents - check if migration is needed
  const archivesTableInfo = db.prepare("PRAGMA table_info(archives)").all() as any[];
  const hasOldColumn = archivesTableInfo.some((col: any) => col.name === 'original_notification_id');
  const hasNewColumn = archivesTableInfo.some((col: any) => col.name === 'original_document_id');

  if (archivesTableInfo.length === 0) {
    // Table doesn't exist, create with new schema
    db.exec(`
      CREATE TABLE archives (
        id TEXT PRIMARY KEY,
        template_name TEXT NOT NULL,
        applicant_name TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        middle_initial TEXT,
        suffix TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        approved_by TEXT,
        original_document_id TEXT,
        metadata TEXT, -- JSON string of additional metadata
        user_id INTEGER REFERENCES users(id)
      )
    `);
    console.log('✅ Created archives table with new schema');
  } else if (hasOldColumn && !hasNewColumn) {
    // Table exists with old schema, migrate it
    console.log('🔄 Migrating archives table from original_notification_id to original_document_id...');

    // Create new table
    db.exec(`
      CREATE TABLE archives_new (
        id TEXT PRIMARY KEY,
        template_name TEXT NOT NULL,
        applicant_name TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        middle_initial TEXT,
        suffix TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        approved_by TEXT,
        original_document_id TEXT,
        metadata TEXT,
        user_id INTEGER REFERENCES users(id)
      )
    `);

    // Copy data
    const copyResult = db.prepare(`
      INSERT INTO archives_new (
        id, template_name, applicant_name, first_name, last_name,
        middle_initial, suffix, created_at, approved_at, approved_by,
        original_document_id, metadata, user_id
      )
      SELECT
        id, template_name, applicant_name, first_name, last_name,
        middle_initial, suffix, created_at, approved_at, approved_by,
        original_notification_id as original_document_id, metadata, user_id
      FROM archives
    `).run();

    // Replace old table
    db.exec('DROP TABLE archives');
    db.exec('ALTER TABLE archives_new RENAME TO archives');

    console.log(`✅ Migrated ${copyResult.changes} archive records to new schema`);
  }

  // Migration: Drop old QR code tables if they exist
  try {
    db.exec('DROP TABLE IF EXISTS qr_codes');
    db.exec('DROP TABLE IF EXISTS qr_validations');
    console.log('Old QR code tables removed successfully');
  } catch (error) {
    console.log('Note: Old QR code tables may not have existed');
  }

  // QR codes table for approved documents validation
  db.exec(`
    CREATE TABLE IF NOT EXISTS document_qr_codes (
      id TEXT PRIMARY KEY,
      document_id TEXT NOT NULL,
      qr_code_data TEXT NOT NULL,
      validation_url TEXT NOT NULL,
      generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      is_active BOOLEAN DEFAULT TRUE,
      FOREIGN KEY (document_id) REFERENCES archives(id) ON DELETE CASCADE
    )
  `);

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_templates_name ON templates(name);
    CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);
    CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
    CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
    CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
    CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
    CREATE INDEX IF NOT EXISTS idx_documents_template_name ON documents(template_name);
    CREATE INDEX IF NOT EXISTS idx_archives_applicant_name ON archives(applicant_name);
    CREATE INDEX IF NOT EXISTS idx_archives_template_name ON archives(template_name);
    CREATE INDEX IF NOT EXISTS idx_archives_approved_at ON archives(approved_at);
    CREATE INDEX IF NOT EXISTS idx_archives_original_document_id ON archives(original_document_id);
    CREATE INDEX IF NOT EXISTS idx_document_qr_codes_document_id ON document_qr_codes(document_id);
    CREATE INDEX IF NOT EXISTS idx_document_qr_codes_generated_at ON document_qr_codes(generated_at);
  `);

  // Run migrations after creating tables
  await runMigrations();
};

// Close database connection
export const closeDatabase = (): void => {
  if (db) {
    db.close();
    db = null;
  }
};

// Utility function to run migrations
export const runMigrations = async (): Promise<void> => {
  const database = await getDatabase();
  
  // Create migrations table if it doesn't exist
  database.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT UNIQUE NOT NULL,
      executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Add any future migrations here
  const migrations: Array<{name: string; sql: string}> = [
    {
      name: '001_add_user_id_to_templates',
      sql: 'ALTER TABLE templates ADD COLUMN user_id INTEGER REFERENCES users(id)'
    },
    {
      name: '003_add_user_id_to_archives',
      sql: 'ALTER TABLE archives ADD COLUMN user_id INTEGER REFERENCES users(id)'
    },
    {
      name: '004_add_user_id_indexes',
      sql: `
        CREATE INDEX IF NOT EXISTS idx_templates_user_id ON templates(user_id);
        CREATE INDEX IF NOT EXISTS idx_archives_user_id ON archives(user_id);
        CREATE INDEX IF NOT EXISTS idx_qr_validations_user_id ON qr_validations(user_id);
      `
    },

    {
      name: '005_migrate_archives_to_qr_validations',
      sql: `
        -- Only migrate if archives table exists and has data
        INSERT OR IGNORE INTO qr_validations (
          id, template_name, applicant_name, barangay, ctc_number, or_number,
          first_name, last_name, middle_initial, approved_at, approved_by,
          original_notification_id, user_id, created_at, updated_at
        )
        SELECT
          id, template_name, applicant_name,
          '' as barangay,  -- Archives table doesn't have barangay column
          '' as ctc_number, -- Archives table doesn't have ctc_number column
          '' as or_number,  -- Archives table doesn't have or_number column
          first_name, last_name, middle_initial, approved_at, approved_by,
          original_notification_id,
          COALESCE(user_id, NULL) as user_id,
          created_at,
          COALESCE(updated_at, created_at) as updated_at
        FROM archives
        WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='archives');
      `
    },

    {
      name: '007_remove_unused_archives_columns',
      sql: `
        -- Create new archives table without unused columns
        CREATE TABLE archives_new (
          id TEXT PRIMARY KEY,
          template_name TEXT NOT NULL,
          applicant_name TEXT NOT NULL,
          first_name TEXT,
          last_name TEXT,
          middle_initial TEXT,
          suffix TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          approved_by TEXT,
          original_notification_id TEXT,
          metadata TEXT, -- JSON string of additional metadata
          user_id INTEGER REFERENCES users(id)
        );

        -- Copy data from old table to new table
        INSERT INTO archives_new (
          id, template_name, applicant_name, first_name, last_name,
          middle_initial, suffix, created_at, approved_at, approved_by,
          original_notification_id, metadata, user_id
        )
        SELECT
          id, template_name, applicant_name, first_name, last_name,
          middle_initial, suffix, created_at, approved_at, approved_by,
          original_notification_id, metadata, user_id
        FROM archives;

        -- Drop old table and rename new table
        DROP TABLE archives;
        ALTER TABLE archives_new RENAME TO archives;

        -- Recreate index
        CREATE INDEX IF NOT EXISTS idx_archives_user_id ON archives(user_id);
      `
    },
    {
      name: '005_remove_invalid_notification_indexes',
      sql: `
        DROP INDEX IF EXISTS idx_notifications_user_id;
      `
    },
    {
      name: '006_replace_admin_id_with_user_id_in_notifications',
      sql: `
        -- Add user_id column to notifications table
        ALTER TABLE notifications ADD COLUMN user_id INTEGER REFERENCES users(id);

        -- Copy admin_id values to user_id (if needed for data preservation)
        -- UPDATE notifications SET user_id = CAST(admin_id AS INTEGER) WHERE admin_id IS NOT NULL AND admin_id != '';

        -- Create index for user_id
        CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
      `
    },
    {
      name: '008_create_documents_table_and_remove_pdf_data_from_notifications',
      sql: `
        -- Create documents table as the main holder for document data
        CREATE TABLE IF NOT EXISTS documents (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
          is_read BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          pdf_filename TEXT,
          pdf_data TEXT, -- JSON string of embedded PDF data
          user_id INTEGER REFERENCES users(id), -- Track which user/admin performed actions
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
          approved_at DATETIME,
          approved_by TEXT
        );

        -- Create indexes for documents table
        CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
        CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
        CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
      `
    },
    {
      name: '009_migrate_notifications_to_new_structure',
      sql: `
        -- Check if old notifications table has pdf_data column
        -- If it does, we need to migrate the data

        -- First, try to migrate data with pdf_data to documents table (if column exists)
        INSERT OR IGNORE INTO documents (
          id, title, message, type, is_read, created_at, updated_at,
          pdf_filename, pdf_data, user_id, status
        )
        SELECT
          n.id, n.title, n.message, n.type, n.is_read, n.created_at, n.updated_at,
          COALESCE(n.pdf_filename, ''), COALESCE(n.pdf_data, ''), COALESCE(n.user_id, NULL), 'pending'
        FROM notifications n
        WHERE EXISTS (
          SELECT 1 FROM pragma_table_info('notifications') WHERE name = 'pdf_data'
        ) AND n.pdf_data IS NOT NULL AND n.pdf_data != '';

        -- Create new notifications table structure if it doesn't match
        CREATE TABLE IF NOT EXISTS notifications_new (
          id TEXT PRIMARY KEY,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
          is_read BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          pdf_filename TEXT,
          user_id INTEGER REFERENCES users(id), -- Track which user/admin performed actions
          document_id TEXT REFERENCES documents(id) -- Reference to document if applicable
        );

        -- Copy all notifications data to new table structure
        INSERT OR IGNORE INTO notifications_new (
          id, title, message, type, is_read, created_at, updated_at,
          pdf_filename, user_id, document_id
        )
        SELECT
          n.id, n.title, n.message, n.type, n.is_read, n.created_at, n.updated_at,
          COALESCE(n.pdf_filename, ''), COALESCE(n.user_id, NULL),
          CASE
            WHEN EXISTS (SELECT 1 FROM pragma_table_info('notifications') WHERE name = 'pdf_data')
                 AND n.pdf_data IS NOT NULL AND n.pdf_data != ''
            THEN n.id
            ELSE NULL
          END
        FROM notifications n;

        -- Only drop and rename if we actually created a new table with different structure
        -- Check if the current notifications table has pdf_data column
        DROP TABLE IF EXISTS notifications_old;
        ALTER TABLE notifications RENAME TO notifications_old;
        ALTER TABLE notifications_new RENAME TO notifications;
        DROP TABLE IF EXISTS notifications_old;

        -- Recreate indexes for notifications table
        CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
        CREATE INDEX IF NOT EXISTS idx_notifications_document_id ON notifications(document_id);
      `
    },
    {
      name: '009_remove_qr_scan_tracking',
      sql: `
        -- Remove scan tracking columns from document_qr_codes table
        -- SQLite doesn't support DROP COLUMN, so we need to recreate the table

        -- Create new table without scan tracking columns
        CREATE TABLE IF NOT EXISTS document_qr_codes_new (
          id TEXT PRIMARY KEY,
          document_id TEXT NOT NULL,
          qr_code_data TEXT NOT NULL,
          validation_url TEXT NOT NULL,
          generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          is_active BOOLEAN DEFAULT TRUE,
          FOREIGN KEY (document_id) REFERENCES archives(id) ON DELETE CASCADE
        );

        -- Copy data from old table to new table (excluding scan tracking columns)
        INSERT OR IGNORE INTO document_qr_codes_new (
          id, document_id, qr_code_data, validation_url, generated_at, is_active
        )
        SELECT
          id, document_id, qr_code_data, validation_url, generated_at, is_active
        FROM document_qr_codes;

        -- Drop old table and rename new table
        DROP TABLE IF EXISTS document_qr_codes;
        ALTER TABLE document_qr_codes_new RENAME TO document_qr_codes;

        -- Recreate indexes
        CREATE INDEX IF NOT EXISTS idx_document_qr_codes_document_id ON document_qr_codes(document_id);
        CREATE INDEX IF NOT EXISTS idx_document_qr_codes_generated_at ON document_qr_codes(generated_at);
      `
    },
    {
      name: '010_update_archives_to_use_document_id',
      sql: `
        -- Update archives table to use original_document_id instead of original_notification_id
        -- Check if the old column exists first
        PRAGMA table_info(archives);

        -- Only proceed if archives table exists and has the old column
        CREATE TABLE IF NOT EXISTS archives_new (
          id TEXT PRIMARY KEY,
          template_name TEXT NOT NULL,
          applicant_name TEXT NOT NULL,
          first_name TEXT,
          last_name TEXT,
          middle_initial TEXT,
          suffix TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          approved_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          approved_by TEXT,
          original_document_id TEXT,
          metadata TEXT,
          user_id INTEGER REFERENCES users(id)
        );

        -- Copy data from old table to new table if old table exists
        INSERT OR IGNORE INTO archives_new (
          id, template_name, applicant_name, first_name, last_name,
          middle_initial, suffix, created_at, approved_at, approved_by,
          original_document_id, metadata, user_id
        )
        SELECT
          id, template_name, applicant_name, first_name, last_name,
          middle_initial, suffix, created_at, approved_at, approved_by,
          COALESCE(original_notification_id, original_document_id) as original_document_id,
          metadata, user_id
        FROM archives
        WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='archives');

        -- Drop old table and rename new table
        DROP TABLE IF EXISTS archives;
        ALTER TABLE archives_new RENAME TO archives;

        -- Recreate indexes
        CREATE INDEX IF NOT EXISTS idx_archives_applicant_name ON archives(applicant_name);
        CREATE INDEX IF NOT EXISTS idx_archives_template_name ON archives(template_name);
        CREATE INDEX IF NOT EXISTS idx_archives_approved_at ON archives(approved_at);
        CREATE INDEX IF NOT EXISTS idx_archives_original_document_id ON archives(original_document_id);
      `
    },
    {
      name: '011_rename_documents_title_to_template_name',
      sql: `
        -- Rename title column to template_name in documents table for consistency
        -- Create new documents table with updated schema
        CREATE TABLE IF NOT EXISTS documents_new (
          id TEXT PRIMARY KEY,
          template_name TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT NOT NULL CHECK (type IN ('info', 'success', 'warning', 'error')),
          is_read BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          pdf_filename TEXT,
          pdf_data TEXT,
          user_id INTEGER REFERENCES users(id),
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
          approved_at DATETIME,
          approved_by TEXT
        );

        -- Copy data from old table to new table, renaming the column
        INSERT INTO documents_new (
          id, template_name, message, type, is_read, created_at, updated_at,
          pdf_filename, pdf_data, user_id, status, approved_at, approved_by
        )
        SELECT
          id, title as template_name, message, type, is_read, created_at, updated_at,
          pdf_filename, pdf_data, user_id, status, approved_at, approved_by
        FROM documents;

        -- Drop old table and rename new table
        DROP TABLE IF EXISTS documents;
        ALTER TABLE documents_new RENAME TO documents;

        -- Recreate indexes
        CREATE INDEX IF NOT EXISTS idx_documents_user_id ON documents(user_id);
        CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
        CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
        CREATE INDEX IF NOT EXISTS idx_documents_template_name ON documents(template_name);
      `
    }
  ];

  for (const migration of migrations) {
    const existing = database.prepare('SELECT name FROM migrations WHERE name = ?').get(migration.name);

    if (!existing) {
      database.exec(migration.sql);
      database.prepare('INSERT INTO migrations (name) VALUES (?)').run(migration.name);
      console.log(`Migration ${migration.name} executed successfully`);
    }
  }
};

// Database maintenance function
export const optimizeDatabase = async (): Promise<void> => {
  const database = await getDatabase();

  try {
    console.log('Starting database optimization...');

    // Clean up orphaned documents (documents without valid templates)
    const orphanedDocs = database.prepare(`
      DELETE FROM documents
      WHERE template_id NOT IN (SELECT id FROM templates)
    `).run();

    if (orphanedDocs.changes > 0) {
      console.log(`Cleaned up ${orphanedDocs.changes} orphaned documents`);
    }

    // Clean up orphaned archives (archives without valid document references)
    const orphanedArchives = database.prepare(`
      DELETE FROM archives
      WHERE original_document_id IS NOT NULL
      AND original_document_id NOT IN (SELECT id FROM documents)
    `).run();

    if (orphanedArchives.changes > 0) {
      console.log(`Cleaned up ${orphanedArchives.changes} orphaned archives`);
    }

    // Vacuum the database to reclaim space
    database.exec('VACUUM');

    // Update statistics for query optimization
    database.exec('ANALYZE');

    console.log('Database optimization completed successfully');
  } catch (error) {
    console.error('Database optimization failed:', error);
    throw error;
  }
};

// Archive-related interfaces and functions
export interface ArchivedDocument {
  id: string;
  template_name: string;
  applicant_name: string;
  first_name?: string;
  last_name?: string;
  middle_initial?: string;
  suffix?: string;
  created_at: string;
  approved_at: string;
  approved_by?: string;
  original_document_id?: string;
  metadata?: string;
  user_id?: number;
}

export interface ArchiveSearchParams {
  search?: string;
  template_name?: string;
  sort_by?: 'approved_at' | 'applicant_name' | 'template_name';
  sort_order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// Archive a document from notification
export const archiveDocument = async (notificationData: any, approvedBy?: string): Promise<ArchivedDocument> => {
  const database = await getDatabase();

  const stmt = database.prepare(`
    INSERT INTO archives (
      id, template_name, applicant_name, first_name, last_name,
      middle_initial, suffix, created_at, approved_at, approved_by,
      original_document_id, metadata, user_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const archivedDoc: ArchivedDocument = {
    id: notificationData.id,
    template_name: notificationData.templateName || '',
    applicant_name: notificationData.applicantName || [
      notificationData.firstName || '',
      notificationData.middleInitial ? `${notificationData.middleInitial}.` : '',
      notificationData.lastName || ''
    ].filter(name => name.trim()).join(' ').trim() || 'Unknown Applicant',
    first_name: notificationData.firstName || null,
    last_name: notificationData.lastName || null,
    middle_initial: notificationData.middleInitial || null,
    suffix: notificationData.suffix || null,
    created_at: notificationData.createdAt ?
      (typeof notificationData.createdAt === 'string' ? notificationData.createdAt : new Date(notificationData.createdAt).toISOString()) :
      new Date().toISOString(),
    approved_at: new Date().toISOString(),
    approved_by: approvedBy,
    original_document_id: notificationData.id,
    metadata: JSON.stringify({
      ...notificationData,
      // Store the complete PDF data for reconstruction
      pdfData: notificationData.pdfData || null,
      templateId: notificationData.templateId || notificationData.id
    }),
    user_id: notificationData.userId || null
  };

  stmt.run(
    archivedDoc.id,
    archivedDoc.template_name,
    archivedDoc.applicant_name,
    archivedDoc.first_name,
    archivedDoc.last_name,
    archivedDoc.middle_initial,
    archivedDoc.suffix,
    archivedDoc.created_at,
    archivedDoc.approved_at,
    archivedDoc.approved_by,
    archivedDoc.original_document_id,
    archivedDoc.metadata,
    archivedDoc.user_id
  );

  return archivedDoc;
};

// Search archived documents
export const searchArchives = async (params: ArchiveSearchParams = {}): Promise<{
  documents: ArchivedDocument[];
  total: number;
}> => {
  const database = await getDatabase();

  const {
    search = '',
    template_name,
    sort_by = 'approved_at',
    sort_order = 'desc',
    limit = 50,
    offset = 0
  } = params;

  let whereClause = 'WHERE 1=1';
  const queryParams: any[] = [];

  // Search in applicant name, template name, first name, last name, middle initial
  if (search) {
    whereClause += ` AND (
      applicant_name LIKE ? OR
      first_name LIKE ? OR
      last_name LIKE ? OR
      middle_initial LIKE ? OR
      template_name LIKE ?
    )`;
    const searchTerm = `%${search}%`;
    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
  }

  if (template_name) {
    whereClause += ' AND template_name = ?';
    queryParams.push(template_name);
  }

  const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;

  // Get total count
  const countStmt = database.prepare(`SELECT COUNT(*) as count FROM archives ${whereClause}`);
  const { count: total } = countStmt.get(...queryParams) as { count: number };

  // Get documents
  const stmt = database.prepare(`
    SELECT * FROM archives
    ${whereClause}
    ${orderClause}
    LIMIT ? OFFSET ?
  `);

  const documents = stmt.all(...queryParams, limit, offset) as ArchivedDocument[];

  return { documents, total };
};

// Get unique values for filters
export const getArchiveFilters = async () => {
  try {
    const database = await getDatabase();

    const templateNames = database.prepare("SELECT DISTINCT template_name FROM archives WHERE template_name IS NOT NULL AND TRIM(template_name) != '' ORDER BY template_name").all() as { template_name: string }[];

    return {
      templateNames: templateNames
        .map(t => t.template_name)
        .filter(name => name && name.trim() !== '') // Additional filter for safety
    };
  } catch (error) {
    console.error('Error fetching archive filters:', error);
    return {
      templateNames: []
    };
  }
};

// Get archived document by ID
export const getArchivedDocument = async (id: string): Promise<ArchivedDocument | null> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM archives WHERE id = ?');
  return stmt.get(id) as ArchivedDocument | null;
};

// Delete archived document
export const deleteArchivedDocument = async (id: string): Promise<boolean> => {
  const database = await getDatabase();
  const stmt = database.prepare('DELETE FROM archives WHERE id = ?');
  const result = stmt.run(id);
  return result.changes > 0;
};

// Document QR Code interfaces and functions
export interface DocumentQRCode {
  id: string;
  document_id: string;
  qr_code_data: string;
  validation_url: string;
  generated_at: string;
  is_active: boolean;
}

export interface CreateDocumentQRCodeData {
  document_id: string;
  qr_code_data: string;
  validation_url: string;
}

// Create QR code for approved document
export const createDocumentQRCode = async (qrData: CreateDocumentQRCodeData): Promise<DocumentQRCode> => {
  const database = await getDatabase();
  const id = generateUUID();

  const stmt = database.prepare(`
    INSERT INTO document_qr_codes (
      id, document_id, qr_code_data, validation_url
    ) VALUES (?, ?, ?, ?)
  `);

  stmt.run(
    id,
    qrData.document_id,
    qrData.qr_code_data,
    qrData.validation_url
  );

  return {
    id,
    ...qrData,
    generated_at: new Date().toISOString(),
    is_active: true
  };
};

// Get QR code by document ID
export const getDocumentQRCode = async (documentId: string): Promise<DocumentQRCode | null> => {
  const database = await getDatabase();
  const stmt = database.prepare('SELECT * FROM document_qr_codes WHERE document_id = ? AND is_active = 1 ORDER BY generated_at DESC LIMIT 1');
  return stmt.get(documentId) as DocumentQRCode | null;
};





// Reset database - DROP ALL TABLES and recreate them
export const resetDatabase = async (): Promise<void> => {
  if (typeof window !== 'undefined') {
    throw new Error('resetDatabase can only be called on the server side');
  }

  try {
    console.log('🔄 Starting database reset...');

    // Close existing connection if any
    if (db) {
      db.close();
      db = null;
    }

    // Delete the database file completely
    try {
      await fs.unlink(DB_PATH);
      console.log('🗑️ Deleted existing database file');
    } catch (error) {
      console.log('📝 No existing database file to delete');
    }

    // Reinitialize the database (this will create a new empty database)
    await initDatabase();

    console.log('✅ Database reset completed successfully!');
    console.log('📊 All tables have been recreated empty');

  } catch (error) {
    console.error('❌ Error resetting database:', error);
    throw error;
  }
};




